(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[812],{839:()=>{},3168:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var a=s(5155),r=s(2115),n=s(7168),i=s(9946);let l=(0,i.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),o=["The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned."],c=["To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face."],d=["Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .","AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .","Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .","Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer ."],u=e=>{let{candidateName:t="Jonathan",jobTitle:s="Insurance Agent",languages:i=["English","Chinese"],instructions:u=o,environmentChecklist:m=c,disclaimers:x=d,onNext:h}=e,[p,g]=(0,r.useState)(!1);return(0,a.jsx)("div",{className:"flex-1 border border-gray-400 rounded-md h-fit bg-white",children:(0,a.jsxs)("div",{className:"p-4 flex flex-col text-[#38383a]",children:[(0,a.jsx)("p",{className:"font-semibold mb-8 text-xl",children:"Instructions for Interview!"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:" mb-2 text-md",children:["Hello ",t,"!"]}),(0,a.jsxs)("p",{className:"text-sm mb-4",children:["As part of the process you are required to complete an AI video assessment for the role of the ",s,"."]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Interview Language"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:i.map((e,t)=>(0,a.jsx)("li",{children:e},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Instructions"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:u.map((e,t)=>(0,a.jsx)("li",{children:e},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Environment Checklist:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:m.map((e,t)=>(0,a.jsx)("li",{children:e},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Important Disclaimers:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:x.map((e,t)=>(0,a.jsx)("li",{children:e},t))})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2 mt-6",children:[(0,a.jsx)("input",{type:"checkbox",id:"terms",checked:p,onChange:e=>g(e.target.checked),className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsxs)("label",{htmlFor:"terms",className:"text-[11px] text-[#38383a]",children:["By checking this box, you agree with AI Interview"," ",(0,a.jsx)("span",{className:"text-primary cursor-pointer font-medium",children:"Terms of use"}),"."]})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(n.$,{disabled:!p,variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>h&&h(),children:["Proceed",(0,a.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})})},m=(0,i.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var x=s(6325);let h=()=>(0,a.jsx)("div",{className:"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"UX/UI Designer for Ai-Interview Web App"}),(0,a.jsxs)("div",{className:"flex gap-2 leading-relaxed mb-3 flex-wrap",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600 font-medium",children:["$500 - $1000 ",(0,a.jsx)("span",{className:"font-extrabold px-1",children:"\xb7"})]}),(0,a.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,a.jsx)(m,{className:"w-4 h-5"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"New York"})]}),(0,a.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,a.jsx)(x.A,{className:"w-4 h-5"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"Onsite / Remote"})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"We're building an AI-powered interview tool. We expect you to help users prepare by giving human interview experience generation."})]}),(0,a.jsx)("span",{className:"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium",children:"Active"})]})}),p=(0,r.createContext)(void 0),g=e=>{let{children:t}=e,[s,n]=(0,r.useState)(null),[i,l]=(0,r.useState)(!1),[o,c]=(0,r.useState)(null),[d,u]=(0,r.useState)(1),[m,x]=(0,r.useState)(!1),h=()=>{let e="******************************:rMMu3KcLvThOQcPwNtrcl";return console.log("Using D-ID API Key:",e?"".concat(e.substring(0,10),"..."):"NOT_FOUND"),{Authorization:"Basic ".concat(e),"Content-Type":"application/json"}},g=(0,r.useCallback)(async(e,t)=>{if(s&&s.llm.instructions===e&&s.preview_name===t)return;l(!0),c(null);let a={presenter:{type:"talk",voice:{type:"microsoft",voice_id:"en-US-JennyMultilingualV2Neural"},thumbnail:"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg",source_url:"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg"},llm:{type:"openai",provider:"openai",model:"gpt-4o-mini",instructions:e},preview_name:t};try{console.log("Creating D-ID Agent with payload:",a);let e=await fetch("".concat("https://api.d-id.com","/agents"),{method:"POST",headers:h(),body:JSON.stringify(a)});if(console.log("D-ID Agent API Response Status:",e.status),!e.ok){let t=await e.text();throw console.error("D-ID Agent API Error Response:",t),Error("Failed to create agent: ".concat(e.status," ").concat(e.statusText," - ").concat(t))}let t=await e.json();console.log("D-ID Agent Created Successfully:",t),n(t)}catch(t){console.error("D-ID Agent Creation Error:",t);let e=t instanceof Error?t.message:"Failed to create agent";c("Agent Creation Failed: ".concat(e))}finally{l(!1)}},[s]);return(0,a.jsx)(p.Provider,{value:{agent:s,isCreatingAgent:i,agentError:o,createAgent:g,currentQuestion:d,setCurrentQuestion:u,isInterviewStarted:m,setIsInterviewStarted:x,questions:["Tell us about yourself?","What are your strengths?","Why do you want this job?","Where do you see yourself in 5 years?"]},children:t})},f=()=>{let e=(0,r.useContext)(p);if(void 0===e)throw Error("useInterview must be used within an InterviewProvider");return e},v=e=>{let{className:t}=e,{questions:s,currentQuestion:r}=f();return(0,a.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ".concat(t||""),children:[" ",(0,a.jsx)("h3",{className:"font-semibold text-lg mb-6",children:"Questions"}),(0,a.jsx)("ul",{className:"relative space-y-8  ",children:Array.from({length:4},(e,t)=>(0,a.jsxs)("li",{className:"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5",children:[3!==t&&(0,a.jsx)("span",{className:"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]"}),(0,a.jsx)("div",{className:"rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ".concat(t+1===r?"bg-[#6938EF] text-white":t+1<r?"bg-green-500 text-white":"bg-[#C7ACF5] text-white"),children:t+1<r?"✓":t+1}),(0,a.jsx)("span",{className:"text-md font-medium mt-7 ".concat(t+1===r?"text-[#6938EF] font-semibold":"text-[#616161]"),children:s[t]})]},t))})]})};var b=s(6766);let y=(0,i.A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),j=(0,i.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),w=e=>{var t;let{className:s="",candidateName:n="Jonathan",jobTitle:i="Insurance Agent",useAgent:l=!0,message:o}=e,{agent:c,isCreatingAgent:d,agentError:u,createAgent:m}=f(),x="You are an AI interview assistant conducting an interview for the ".concat(i," position with ").concat(n,". Be professional, engaging, and ask relevant questions about their experience and qualifications."),h="".concat(i," Interviewer");return(0,r.useEffect)(()=>{!l||c||d||m(x,h)},[l,c,d,m,x,h]),(0,a.jsx)("div",{className:"relative ".concat(s),children:(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden",children:c?(0,a.jsx)("div",{className:"text-center w-full h-full flex flex-col",children:(0,a.jsxs)("div",{className:"flex-1 flex items-center justify-center p-4",children:[(null==(t=c.presenter)?void 0:t.thumbnail)?(0,a.jsx)(b.default,{src:c.presenter.thumbnail,alt:c.preview_name,width:320,height:550,className:"w-full h-full object-cover rounded-2xl shadow-lg max-w-xs max-h-80",onError:e=>{var t;console.error("Failed to load avatar image:",c.presenter.thumbnail),e.currentTarget.style.display="none",null==(t=e.currentTarget.nextElementSibling)||t.classList.remove("hidden")}}):(0,a.jsx)("div",{className:"w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center",children:(0,a.jsx)(y,{className:"w-16 h-16 text-gray-600"})}),(0,a.jsx)("div",{className:"hidden w-32 h-32 bg-gray-300 rounded-full items-center justify-center",children:(0,a.jsx)(y,{className:"w-16 h-16 text-gray-600"})})]})}):d?(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(j,{className:"w-12 h-12 animate-spin text-blue-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Creating AI Agent..."}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This may take a moment"})]}):u?(0,a.jsxs)("div",{className:"text-center p-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(y,{className:"w-8 h-8 text-red-500"})}),(0,a.jsx)("p",{className:"text-sm text-red-600 mb-2",children:"Failed to create agent"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:u}),(0,a.jsx)("button",{onClick:()=>m(x,h),className:"mt-3 px-4 py-2 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors",children:"Retry"})]}):(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(y,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"No agent available"})]})})})},N=e=>{let{children:t}=e;return(0,a.jsx)("div",{className:"border rounded-lg p-6 min-h-[600px] mb-4 flex-1",children:t})},k=e=>{let{onNext:t}=e;return(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(h,{}),(0,a.jsxs)(N,{children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,a.jsx)(v,{className:"h-[550px]"}),(0,a.jsx)(w,{className:"h-[550px]",useAgent:!0,candidateName:"Jonathan",jobTitle:"Insurance Agent"})]}),(0,a.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,a.jsxs)(n.$,{variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>t&&t(),children:["Start Interview",(0,a.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})},A=()=>(0,a.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden",children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-black mb-5",children:"Video Transcript"}),(0,a.jsx)("p",{children:"Tell us about yourselves?"}),(0,a.jsx)("p",{className:"text-sm mt-4 leading-7 ",children:"Motivated and results-driven professional with a proven track record of success in dynamic work environments. Known for strong problem-solving skills, a collaborative mindset, and a dedication to continuous learning and improvement. Brings a blend of technical expertise, strategic thinking, and effective communication to contribute meaningfully to team and organizational goals. Eager to take on new challenges and deliver impactful outcomes in a fast-paced role."})]}),C=e=>{let{onNext:t}=e;return(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(h,{}),(0,a.jsxs)(N,{children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,a.jsx)(v,{}),(0,a.jsx)(w,{className:" h-[490px]",useAgent:!0,candidateName:"Jonathan",jobTitle:"Insurance Agent",message:"Thank you for completing the interview. Do you have any final questions?"}),(0,a.jsx)(A,{})]}),(0,a.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,a.jsxs)(n.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>t&&t(),children:["Finish Interview",(0,a.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})},I={src:"/_next/static/media/trophy.73528452.png",height:28,width:28,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAANlBMVEXNszH7qED//1HPmRGtnSfDoyrn1T1MaXHr1j+cqzrVpR7SoiDosSnluR7esSLrwyPptBvv0S75zPcvAAAAEnRSTlMR/hacHCkqADMIjM+nl9x4XaVFuRFRAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPElEQVR4nBXFSRIAIQgAsUZBwN3/f3ZqcglJMSskhKpq/Pe9uwZWn8irRrtLZN0GkedkgLecM5vjzhi4fzkhAbtZdsbsAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},E=()=>(0,a.jsxs)("div",{className:"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30",children:[(0,a.jsx)("div",{className:"flex justify-center mb-2",children:(0,a.jsx)(b.default,{src:I,alt:"Trophy"})}),(0,a.jsx)("p",{className:"text-xl font-bold text-[#1E1E1E]",children:"55%"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:"Overall Score"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2",children:"AI Interviewer"}),(0,a.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"UI UX Designer"}),(0,a.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"18th June, 2025"})]})]}),(0,a.jsx)("div",{className:"top-0",children:(0,a.jsx)("span",{className:"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full",children:"Evaluated"})})]}),R=e=>{let{label:t,value:s,color:r="bg-orange-500"}=e;return(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"mb-1",children:t}),(0,a.jsxs)("span",{children:[s,"/100"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,a.jsx)("div",{className:"h-2.5 rounded-full ".concat(r),style:{width:"".concat(s,"%")}})})]})};var P=function(e,t){return(P=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var s in t)t.hasOwnProperty(s)&&(e[s]=t[s])})(e,t)};function S(e){var t,s,a,n,i,l,o,c,d=e.className,u=e.counterClockwise,m=e.dashRatio,x=e.pathRadius,h=e.strokeWidth,p=e.style;return(0,r.createElement)("path",{className:d,style:Object.assign({},p,(s=(t={pathRadius:x,dashRatio:m,counterClockwise:u}).counterClockwise,a=t.dashRatio,i=(1-a)*(n=2*Math.PI*t.pathRadius),{strokeDasharray:n+"px "+n+"px",strokeDashoffset:(s?-i:i)+"px"})),d:(o=(l={pathRadius:x,counterClockwise:u}).pathRadius,"\n      M 50,50\n      m 0,-"+o+"\n      a "+o+","+o+" "+(c=+!!l.counterClockwise)+" 1 1 0,"+2*o+"\n      a "+o+","+o+" "+c+" 1 1 0,-"+2*o+"\n    "),strokeWidth:h,fillOpacity:0})}var T=function(e){function t(){this.constructor=s}function s(){return null!==e&&e.apply(this,arguments)||this}return P(s,e),s.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t),s.prototype.getBackgroundPadding=function(){return this.props.background?this.props.backgroundPadding:0},s.prototype.getPathRadius=function(){return 50-this.props.strokeWidth/2-this.getBackgroundPadding()},s.prototype.getPathRatio=function(){var e=this.props,t=e.value,s=e.minValue,a=e.maxValue;return(Math.min(Math.max(t,s),a)-s)/(a-s)},s.prototype.render=function(){var e=this.props,t=e.circleRatio,s=e.className,a=e.classes,n=e.counterClockwise,i=e.styles,l=e.strokeWidth,o=e.text,c=this.getPathRadius(),d=this.getPathRatio();return(0,r.createElement)("svg",{className:a.root+" "+s,style:i.root,viewBox:"0 0 100 100","data-test-id":"CircularProgressbar"},this.props.background?(0,r.createElement)("circle",{className:a.background,style:i.background,cx:50,cy:50,r:50}):null,(0,r.createElement)(S,{className:a.trail,counterClockwise:n,dashRatio:t,pathRadius:c,strokeWidth:l,style:i.trail}),(0,r.createElement)(S,{className:a.path,counterClockwise:n,dashRatio:d*t,pathRadius:c,strokeWidth:l,style:i.path}),o?(0,r.createElement)("text",{className:a.text,style:i.text,x:50,y:50},o):null)},s.defaultProps={background:!1,backgroundPadding:0,circleRatio:1,classes:{root:"CircularProgressbar",trail:"CircularProgressbar-trail",path:"CircularProgressbar-path",text:"CircularProgressbar-text",background:"CircularProgressbar-background"},counterClockwise:!1,className:"",maxValue:100,minValue:0,strokeWidth:8,styles:{root:{},trail:{},path:{},text:{},background:{}},text:""},s}(r.Component);function F(e){return Object.keys(e).forEach(function(t){null==e[t]&&delete e[t]}),e}s(839);let M=e=>{let{label:t,percent:s,color:r,trailColor:n}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-1 mb-2",children:[(0,a.jsx)("p",{className:"text-sm font-semibold mb-3",children:t}),(0,a.jsx)("div",{className:"w-32 h-28",children:(0,a.jsx)(T,{value:s,text:"".concat(s,"%"),strokeWidth:10,styles:function(e){var t=e.rotation,s=e.strokeLinecap,a=e.textColor,r=e.textSize,n=e.pathColor,i=e.pathTransition,l=e.pathTransitionDuration,o=e.trailColor,c=e.backgroundColor,d=null==t?void 0:"rotate("+t+"turn)",u=null==t?void 0:"center center";return{root:{},path:F({stroke:n,strokeLinecap:s,transform:d,transformOrigin:u,transition:i,transitionDuration:null==l?void 0:l+"s"}),trail:F({stroke:o,strokeLinecap:s,transform:d,transformOrigin:u}),text:F({fill:a,fontSize:r}),background:F({fill:c})}}({textSize:"12px",pathColor:r,textColor:"#5a5a5a",trailColor:n})})})]})},D=()=>(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between font-semibold mb-4",children:[(0,a.jsx)("span",{children:"Resume Score"}),(0,a.jsx)("span",{children:"65%"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)(R,{label:"Company Fit",value:66}),(0,a.jsx)(R,{label:"Relevant Experience",value:66,color:"bg-purple-600"}),(0,a.jsx)(R,{label:"Job Knowledge",value:66}),(0,a.jsx)(R,{label:"Education",value:66}),(0,a.jsx)(R,{label:"Hard Skills",value:66})]}),(0,a.jsxs)("div",{className:"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8",children:["Over All Score \xa0 ",(0,a.jsx)("span",{className:"text-black",children:"66/100"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsx)("div",{className:"font-semibold mb-4",children:"Video Score"}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)(R,{label:"Professionalism",value:64}),(0,a.jsx)(R,{label:"Energy Level",value:56,color:"bg-purple-600"}),(0,a.jsx)(R,{label:"Communication",value:58}),(0,a.jsx)(R,{label:"Sociability",value:70})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm",children:[(0,a.jsx)("p",{className:"font-semibold",children:"AI Rating"}),(0,a.jsx)(M,{label:"AI Resume Rating",percent:75,color:"#A855F7",trailColor:"#EAE2FF"}),(0,a.jsx)(M,{label:"AI Video Rating",percent:75,color:"#FF5B00",trailColor:"#FFEAE1"})]})]}),q=()=>(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(E,{}),(0,a.jsx)(N,{children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,a.jsx)(v,{}),(0,a.jsx)(w,{className:"h-[490px]",useAgent:!1,candidateName:"Jonathan",jobTitle:"Insurance Agent"}),(0,a.jsx)(A,{})]})}),(0,a.jsx)(D,{})]}),_=e=>{let{onNext:t}=e;return(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(h,{}),(0,a.jsxs)(N,{children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start",children:[(0,a.jsx)(v,{className:"h-[550px]"}),(0,a.jsx)(w,{className:"h-[550px]",useAgent:!0,candidateName:"Jonathan",jobTitle:"Insurance Agent"})]}),(0,a.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,a.jsxs)(n.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>t&&t(),children:["Start Interview",(0,a.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})}),(0,a.jsx)("div",{className:"flex justify-center mt-5 text-2xl font-semibold text-primary",children:"02:00"})]})]})},z=()=>{let[e,t]=(0,r.useState)("instructions");return(0,a.jsx)(g,{children:(0,a.jsx)("div",{children:(()=>{switch(e){case"instructions":default:return(0,a.jsx)(u,{onNext:()=>t("questions")});case"questions":return(0,a.jsx)(k,{onNext:()=>t("recording")});case"recording":return(0,a.jsx)(_,{onNext:()=>t("finishInterview")});case"finishInterview":return(0,a.jsx)(C,{onNext:()=>t("analysis")});case"analysis":return(0,a.jsx)(q,{})}})()})})}},3999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},6149:(e,t,s)=>{Promise.resolve().then(s.bind(s,3168))},6325:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("briefcase-business",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},7168:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(5155);s(2115);var r=s(4624),n=s(2085),i=s(3999);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:s,size:n,className:t})),...c})}},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var a=s(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:d="",children:u,iconNode:m,...x}=e;return(0,a.createElement)("svg",{ref:t,...c,width:r,height:r,stroke:s,strokeWidth:i?24*Number(n)/Number(r):n,className:l("lucide",d),...!u&&!o(x)&&{"aria-hidden":"true"},...x},[...m.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let s=(0,a.forwardRef)((s,n)=>{let{className:o,...c}=s;return(0,a.createElement)(d,{ref:n,iconNode:t,className:l("lucide-".concat(r(i(e))),"lucide-".concat(e),o),...c})});return s.displayName=i(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[318,13,766,441,684,358],()=>t(6149)),_N_E=e.O()}]);