(()=>{var e={};e.id=812,e.ids=[812],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1079:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(root)\\\\interview\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx","default")},1900:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var r=s(687),a=s(3210),i=s(4934),n=s(2688);let l=(0,n.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),o=["The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned."],c=["To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face."],d=["Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .","AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .","Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .","Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer ."],u=({candidateName:e="Jonathan",jobTitle:t="Insurance Agent",languages:s=["English","Chinese"],instructions:n=o,environmentChecklist:u=c,disclaimers:x=d,onNext:m})=>{let[h,p]=(0,a.useState)(!1);return(0,r.jsx)("div",{className:"flex-1 border border-gray-400 rounded-md h-fit bg-white",children:(0,r.jsxs)("div",{className:"p-4 flex flex-col text-[#38383a]",children:[(0,r.jsx)("p",{className:"font-semibold mb-8 text-xl",children:"Instructions for Interview!"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:" mb-2 text-md",children:["Hello ",e,"!"]}),(0,r.jsxs)("p",{className:"text-sm mb-4",children:["As part of the process you are required to complete an AI video assessment for the role of the ",t,"."]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Interview Language"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:s.map((e,t)=>(0,r.jsx)("li",{children:e},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Instructions"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:n.map((e,t)=>(0,r.jsx)("li",{children:e},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Environment Checklist:"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:u.map((e,t)=>(0,r.jsx)("li",{children:e},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Important Disclaimers:"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:x.map((e,t)=>(0,r.jsx)("li",{children:e},t))})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2 mt-6",children:[(0,r.jsx)("input",{type:"checkbox",id:"terms",checked:h,onChange:e=>p(e.target.checked),className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,r.jsxs)("label",{htmlFor:"terms",className:"text-[11px] text-[#38383a]",children:["By checking this box, you agree with AI Interview"," ",(0,r.jsx)("span",{className:"text-primary cursor-pointer font-medium",children:"Terms of use"}),"."]})]}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)(i.$,{disabled:!h,variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>m&&m(),children:["Proceed",(0,r.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})})},x=(0,n.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var m=s(659);let h=()=>(0,r.jsx)("div",{className:"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"UX/UI Designer for Ai-Interview Web App"}),(0,r.jsxs)("div",{className:"flex gap-2 leading-relaxed mb-3 flex-wrap",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600 font-medium",children:["$500 - $1000 ",(0,r.jsx)("span",{className:"font-extrabold px-1",children:"\xb7"})]}),(0,r.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,r.jsx)(x,{className:"w-4 h-5"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"New York"})]}),(0,r.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,r.jsx)(m.A,{className:"w-4 h-5"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"Onsite / Remote"})]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"We're building an AI-powered interview tool. We expect you to help users prepare by giving human interview experience generation."})]}),(0,r.jsx)("span",{className:"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium",children:"Active"})]})}),p=(0,a.createContext)(void 0),g=({children:e})=>{let[t,s]=(0,a.useState)(null),[i,n]=(0,a.useState)(!1),[l,o]=(0,a.useState)(null),[c,d]=(0,a.useState)(1),[u,x]=(0,a.useState)(!1),m=()=>{let e="******************************:rMMu3KcLvThOQcPwNtrcl";return console.log("Using D-ID API Key:",e?`${e.substring(0,10)}...`:"NOT_FOUND"),{Authorization:`Basic ${e}`,"Content-Type":"application/json"}},h=(0,a.useCallback)(async(e,r)=>{if(t&&t.llm.instructions===e&&t.preview_name===r)return;n(!0),o(null);let a={presenter:{type:"talk",voice:{type:"microsoft",voice_id:"en-US-JennyMultilingualV2Neural"},thumbnail:"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg",source_url:"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg"},llm:{type:"openai",provider:"openai",model:"gpt-4o-mini",instructions:e},preview_name:r};try{console.log("Creating D-ID Agent with payload:",a);let e=await fetch("https://api.d-id.com/agents",{method:"POST",headers:m(),body:JSON.stringify(a)});if(console.log("D-ID Agent API Response Status:",e.status),!e.ok){let t=await e.text();throw console.error("D-ID Agent API Error Response:",t),Error(`Failed to create agent: ${e.status} ${e.statusText} - ${t}`)}let t=await e.json();console.log("D-ID Agent Created Successfully:",t),s(t)}catch(t){console.error("D-ID Agent Creation Error:",t);let e=t instanceof Error?t.message:"Failed to create agent";o(`Agent Creation Failed: ${e}`)}finally{n(!1)}},[t]);return(0,r.jsx)(p.Provider,{value:{agent:t,isCreatingAgent:i,agentError:l,createAgent:h,currentQuestion:c,setCurrentQuestion:d,isInterviewStarted:u,setIsInterviewStarted:x,questions:["Tell us about yourself?","What are your strengths?","Why do you want this job?","Where do you see yourself in 5 years?"]},children:e})},f=()=>{let e=(0,a.useContext)(p);if(void 0===e)throw Error("useInterview must be used within an InterviewProvider");return e},v=({className:e})=>{let{questions:t,currentQuestion:s}=f();return(0,r.jsxs)("div",{className:`rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ${e||""}`,children:[" ",(0,r.jsx)("h3",{className:"font-semibold text-lg mb-6",children:"Questions"}),(0,r.jsx)("ul",{className:"relative space-y-8  ",children:Array.from({length:4},(e,a)=>(0,r.jsxs)("li",{className:"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5",children:[3!==a&&(0,r.jsx)("span",{className:"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]"}),(0,r.jsx)("div",{className:`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${a+1===s?"bg-[#6938EF] text-white":a+1<s?"bg-green-500 text-white":"bg-[#C7ACF5] text-white"}`,children:a+1<s?"✓":a+1}),(0,r.jsx)("span",{className:`text-md font-medium mt-7 ${a+1===s?"text-[#6938EF] font-semibold":"text-[#616161]"}`,children:t[a]})]},a))})]})};var b=s(474);let j=(0,n.A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),y=(0,n.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),w=({className:e="",candidateName:t="Jonathan",jobTitle:s="Insurance Agent",useAgent:i=!0,message:n})=>{let{agent:l,isCreatingAgent:o,agentError:c,createAgent:d}=f(),u=`You are an AI interview assistant conducting an interview for the ${s} position with ${t}. Be professional, engaging, and ask relevant questions about their experience and qualifications.`,x=`${s} Interviewer`;return(0,a.useEffect)(()=>{!i||l||o||d(u,x)},[i,l,o,d,u,x]),(0,r.jsx)("div",{className:`relative ${e}`,children:(0,r.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden",children:l?(0,r.jsx)("div",{className:"text-center w-full h-full flex flex-col",children:(0,r.jsxs)("div",{className:"flex-1 flex items-center justify-center p-4",children:[l.presenter?.thumbnail?(0,r.jsx)(b.default,{src:l.presenter.thumbnail,alt:l.preview_name,width:320,height:550,className:"w-full h-full object-cover rounded-2xl shadow-lg max-w-xs max-h-80",onError:e=>{console.error("Failed to load avatar image:",l.presenter.thumbnail),e.currentTarget.style.display="none",e.currentTarget.nextElementSibling?.classList.remove("hidden")}}):(0,r.jsx)("div",{className:"w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center",children:(0,r.jsx)(j,{className:"w-16 h-16 text-gray-600"})}),(0,r.jsx)("div",{className:"hidden w-32 h-32 bg-gray-300 rounded-full items-center justify-center",children:(0,r.jsx)(j,{className:"w-16 h-16 text-gray-600"})})]})}):o?(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(y,{className:"w-12 h-12 animate-spin text-blue-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Creating AI Agent..."}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This may take a moment"})]}):c?(0,r.jsxs)("div",{className:"text-center p-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(j,{className:"w-8 h-8 text-red-500"})}),(0,r.jsx)("p",{className:"text-sm text-red-600 mb-2",children:"Failed to create agent"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:c}),(0,r.jsx)("button",{onClick:()=>d(u,x),className:"mt-3 px-4 py-2 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors",children:"Retry"})]}):(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(j,{className:"w-8 h-8 text-gray-400"})}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"No agent available"})]})})})},N=({children:e})=>(0,r.jsx)("div",{className:"border rounded-lg p-6 min-h-[600px] mb-4 flex-1",children:e}),k=({onNext:e})=>(0,r.jsxs)("div",{className:"h-screen",children:[(0,r.jsx)(h,{}),(0,r.jsxs)(N,{children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,r.jsx)(v,{className:"h-[550px]"}),(0,r.jsx)(w,{className:"h-[550px]",useAgent:!0,candidateName:"Jonathan",jobTitle:"Insurance Agent"})]}),(0,r.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,r.jsxs)(i.$,{variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>e&&e(),children:["Start Interview",(0,r.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]}),A=()=>(0,r.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-black mb-5",children:"Video Transcript"}),(0,r.jsx)("p",{children:"Tell us about yourselves?"}),(0,r.jsx)("p",{className:"text-sm mt-4 leading-7 ",children:"Motivated and results-driven professional with a proven track record of success in dynamic work environments. Known for strong problem-solving skills, a collaborative mindset, and a dedication to continuous learning and improvement. Brings a blend of technical expertise, strategic thinking, and effective communication to contribute meaningfully to team and organizational goals. Eager to take on new challenges and deliver impactful outcomes in a fast-paced role."})]}),C=({onNext:e})=>(0,r.jsxs)("div",{className:"h-screen",children:[(0,r.jsx)(h,{}),(0,r.jsxs)(N,{children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,r.jsx)(v,{}),(0,r.jsx)(w,{className:" h-[490px]",useAgent:!0,candidateName:"Jonathan",jobTitle:"Insurance Agent",message:"Thank you for completing the interview. Do you have any final questions?"}),(0,r.jsx)(A,{})]}),(0,r.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,r.jsxs)(i.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>e&&e(),children:["Finish Interview",(0,r.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]}),I={src:"/_next/static/media/trophy.73528452.png",height:28,width:28,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAANlBMVEXNszH7qED//1HPmRGtnSfDoyrn1T1MaXHr1j+cqzrVpR7SoiDosSnluR7esSLrwyPptBvv0S75zPcvAAAAEnRSTlMR/hacHCkqADMIjM+nl9x4XaVFuRFRAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPElEQVR4nBXFSRIAIQgAsUZBwN3/f3ZqcglJMSskhKpq/Pe9uwZWn8irRrtLZN0GkedkgLecM5vjzhi4fzkhAbtZdsbsAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},E=()=>(0,r.jsxs)("div",{className:"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30",children:[(0,r.jsx)("div",{className:"flex justify-center mb-2",children:(0,r.jsx)(b.default,{src:I,alt:"Trophy"})}),(0,r.jsx)("p",{className:"text-xl font-bold text-[#1E1E1E]",children:"55%"}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:"Overall Score"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2",children:"AI Interviewer"}),(0,r.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"UI UX Designer"}),(0,r.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"18th June, 2025"})]})]}),(0,r.jsx)("div",{className:"top-0",children:(0,r.jsx)("span",{className:"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full",children:"Evaluated"})})]}),P=({label:e,value:t,color:s="bg-orange-500"})=>(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,r.jsx)("span",{className:"mb-1",children:e}),(0,r.jsxs)("span",{children:[t,"/100"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,r.jsx)("div",{className:`h-2.5 rounded-full ${s}`,style:{width:`${t}%`}})})]});var R=function(e,t){return(R=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var s in t)t.hasOwnProperty(s)&&(e[s]=t[s])})(e,t)};function S(e){var t,s,r,i,n,l,o,c,d=e.className,u=e.counterClockwise,x=e.dashRatio,m=e.pathRadius,h=e.strokeWidth,p=e.style;return(0,a.createElement)("path",{className:d,style:Object.assign({},p,(s=(t={pathRadius:m,dashRatio:x,counterClockwise:u}).counterClockwise,r=t.dashRatio,n=(1-r)*(i=2*Math.PI*t.pathRadius),{strokeDasharray:i+"px "+i+"px",strokeDashoffset:(s?-n:n)+"px"})),d:(o=(l={pathRadius:m,counterClockwise:u}).pathRadius,"\n      M 50,50\n      m 0,-"+o+"\n      a "+o+","+o+" "+(c=+!!l.counterClockwise)+" 1 1 0,"+2*o+"\n      a "+o+","+o+" "+c+" 1 1 0,-"+2*o+"\n    "),strokeWidth:h,fillOpacity:0})}var q=function(e){function t(){this.constructor=s}function s(){return null!==e&&e.apply(this,arguments)||this}return R(s,e),s.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t),s.prototype.getBackgroundPadding=function(){return this.props.background?this.props.backgroundPadding:0},s.prototype.getPathRadius=function(){return 50-this.props.strokeWidth/2-this.getBackgroundPadding()},s.prototype.getPathRatio=function(){var e=this.props,t=e.value,s=e.minValue,r=e.maxValue;return(Math.min(Math.max(t,s),r)-s)/(r-s)},s.prototype.render=function(){var e=this.props,t=e.circleRatio,s=e.className,r=e.classes,i=e.counterClockwise,n=e.styles,l=e.strokeWidth,o=e.text,c=this.getPathRadius(),d=this.getPathRatio();return(0,a.createElement)("svg",{className:r.root+" "+s,style:n.root,viewBox:"0 0 100 100","data-test-id":"CircularProgressbar"},this.props.background?(0,a.createElement)("circle",{className:r.background,style:n.background,cx:50,cy:50,r:50}):null,(0,a.createElement)(S,{className:r.trail,counterClockwise:i,dashRatio:t,pathRadius:c,strokeWidth:l,style:n.trail}),(0,a.createElement)(S,{className:r.path,counterClockwise:i,dashRatio:d*t,pathRadius:c,strokeWidth:l,style:n.path}),o?(0,a.createElement)("text",{className:r.text,style:n.text,x:50,y:50},o):null)},s.defaultProps={background:!1,backgroundPadding:0,circleRatio:1,classes:{root:"CircularProgressbar",trail:"CircularProgressbar-trail",path:"CircularProgressbar-path",text:"CircularProgressbar-text",background:"CircularProgressbar-background"},counterClockwise:!1,className:"",maxValue:100,minValue:0,strokeWidth:8,styles:{root:{},trail:{},path:{},text:{},background:{}},text:""},s}(a.Component);function D(e){return Object.keys(e).forEach(function(t){null==e[t]&&delete e[t]}),e}s(9587);let T=({label:e,percent:t,color:s,trailColor:a})=>(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1 mb-2",children:[(0,r.jsx)("p",{className:"text-sm font-semibold mb-3",children:e}),(0,r.jsx)("div",{className:"w-32 h-28",children:(0,r.jsx)(q,{value:t,text:`${t}%`,strokeWidth:10,styles:function(e){var t=e.rotation,s=e.strokeLinecap,r=e.textColor,a=e.textSize,i=e.pathColor,n=e.pathTransition,l=e.pathTransitionDuration,o=e.trailColor,c=e.backgroundColor,d=null==t?void 0:"rotate("+t+"turn)",u=null==t?void 0:"center center";return{root:{},path:D({stroke:i,strokeLinecap:s,transform:d,transformOrigin:u,transition:n,transitionDuration:null==l?void 0:l+"s"}),trail:D({stroke:o,strokeLinecap:s,transform:d,transformOrigin:u}),text:D({fill:r,fontSize:a}),background:D({fill:c})}}({textSize:"12px",pathColor:s,textColor:"#5a5a5a",trailColor:a})})})]}),_=()=>(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between font-semibold mb-4",children:[(0,r.jsx)("span",{children:"Resume Score"}),(0,r.jsx)("span",{children:"65%"})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)(P,{label:"Company Fit",value:66}),(0,r.jsx)(P,{label:"Relevant Experience",value:66,color:"bg-purple-600"}),(0,r.jsx)(P,{label:"Job Knowledge",value:66}),(0,r.jsx)(P,{label:"Education",value:66}),(0,r.jsx)(P,{label:"Hard Skills",value:66})]}),(0,r.jsxs)("div",{className:"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8",children:["Over All Score \xa0 ",(0,r.jsx)("span",{className:"text-black",children:"66/100"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,r.jsx)("div",{className:"font-semibold mb-4",children:"Video Score"}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)(P,{label:"Professionalism",value:64}),(0,r.jsx)(P,{label:"Energy Level",value:56,color:"bg-purple-600"}),(0,r.jsx)(P,{label:"Communication",value:58}),(0,r.jsx)(P,{label:"Sociability",value:70})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm",children:[(0,r.jsx)("p",{className:"font-semibold",children:"AI Rating"}),(0,r.jsx)(T,{label:"AI Resume Rating",percent:75,color:"#A855F7",trailColor:"#EAE2FF"}),(0,r.jsx)(T,{label:"AI Video Rating",percent:75,color:"#FF5B00",trailColor:"#FFEAE1"})]})]}),F=()=>(0,r.jsxs)("div",{className:"h-screen",children:[(0,r.jsx)(E,{}),(0,r.jsx)(N,{children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,r.jsx)(v,{}),(0,r.jsx)(w,{className:"h-[490px]",useAgent:!1,candidateName:"Jonathan",jobTitle:"Insurance Agent"}),(0,r.jsx)(A,{})]})}),(0,r.jsx)(_,{})]}),M=({onNext:e})=>(0,r.jsxs)("div",{className:"h-screen",children:[(0,r.jsx)(h,{}),(0,r.jsxs)(N,{children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start",children:[(0,r.jsx)(v,{className:"h-[550px]"}),(0,r.jsx)(w,{className:"h-[550px]",useAgent:!0,candidateName:"Jonathan",jobTitle:"Insurance Agent"})]}),(0,r.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,r.jsxs)(i.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>e&&e(),children:["Start Interview",(0,r.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})}),(0,r.jsx)("div",{className:"flex justify-center mt-5 text-2xl font-semibold text-primary",children:"02:00"})]})]}),z=()=>{let[e,t]=(0,a.useState)("instructions");return(0,r.jsx)(g,{children:(0,r.jsx)("div",{children:(()=>{switch(e){case"instructions":default:return(0,r.jsx)(u,{onNext:()=>t("questions")});case"questions":return(0,r.jsx)(k,{onNext:()=>t("recording")});case"recording":return(0,r.jsx)(M,{onNext:()=>t("finishInterview")});case"finishInterview":return(0,r.jsx)(C,{onNext:()=>t("analysis")});case"analysis":return(0,r.jsx)(F,{})}})()})})}},2379:(e,t,s)=>{Promise.resolve().then(s.bind(s,1900))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4934:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var r=s(687);s(3210);var a=s(1391),i=s(4224),n=s(6241);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:t,size:s,asChild:i=!1,...o}){let c=i?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:s,className:e})),...o})}},5511:e=>{"use strict";e.exports=require("crypto")},6241:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var r=s(9384),a=s(2348);function i(...e){return(0,a.QP)((0,r.$)(e))}},6723:(e,t,s)=>{Promise.resolve().then(s.bind(s,1079))},7089:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["(root)",{children:["interview",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1079)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2528)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,8014)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(root)/interview/page",pathname:"/interview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9587:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[97,423,715,23,762],()=>s(7089));module.exports=r})();