"use client";
import React, { useEffect } from "react";
import Image from "next/image";
import { Bot, Loader2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useInterview } from "@/context/InterviewContext";

type CandidateWithAgentProps = {
  className?: string;
  candidateName?: string;
  jobTitle?: string;
  useAgent?: boolean;
  message?: string;
  onVideoReady?: () => void;
  onVideoEnd?: () => void;
  useStreaming?: boolean;
  avatarMode?: "standard" | "streaming" | "live";
};

const CandidateWithAgent: React.FC<CandidateWithAgentProps> = ({
  className = "",
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
  useAgent = true,
  message,
}) => {
  const { agent, isCreatingAgent, agentError, createAgent } = useInterview();

  const instructions = `You are an AI interview assistant conducting an interview for the ${jobTitle} position with ${candidateName}. Be professional, engaging, and ask relevant questions about their experience and qualifications.`;
  const agentName = `${jobTitle} Interviewer`;

  useEffect(() => {
    if (useAgent && !agent && !isCreatingAgent) {
      createAgent(instructions, agentName);
    }
  }, [useAgent, agent, isCreatingAgent, createAgent, instructions, agentName]);

  return (
    <div className={`relative ${className}`}>
      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden">
        {agent ? (
          <div className="text-center w-full h-full flex flex-col">
            {/* Avatar Image */}
            <div className="flex-1 flex items-center justify-center p-4">
              {agent.presenter?.thumbnail ? (
                <Image
                  src={agent.presenter.thumbnail}
                  alt={agent.preview_name}
                  width={320}
                  height={550}
                  className="w-full h-full object-cover rounded-2xl shadow-lg max-w-xs max-h-80"
                  onError={(e) => {
                    console.error("Failed to load avatar image:", agent.presenter.thumbnail);
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : (
                <div className="w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center">
                  <Bot className="w-16 h-16 text-gray-600" />
                </div>
              )}

              {/* Fallback icon (hidden by default, shown if image fails) */}
              <div className="hidden w-32 h-32 bg-gray-300 rounded-full items-center justify-center">
                <Bot className="w-16 h-16 text-gray-600" />
              </div>
            </div>

            {/* Agent Info */}
            {/* <div className="p-4 bg-white/80 backdrop-blur-sm">
              <h3 className="font-semibold text-lg text-gray-800">
                {agent.preview_name}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Status: {agent.status}
              </p>
              {message && (
                <p className="text-sm text-blue-600 mt-2 italic">
                  "{message}"
                </p>
              )}
            </div> */}
          </div>
        ) : isCreatingAgent ? (
          <div className="text-center">
            <Loader2 className="w-12 h-12 animate-spin text-blue-500 mx-auto mb-4" />
            <p className="text-sm text-gray-600">Creating AI Agent...</p>
            <p className="text-xs text-gray-500 mt-2">This may take a moment</p>
          </div>
        ) : agentError ? (
          <div className="text-center p-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Bot className="w-8 h-8 text-red-500" />
            </div>
            <p className="text-sm text-red-600 mb-2">Failed to create agent</p>
            <p className="text-xs text-gray-500">{agentError}</p>
            <button
              onClick={() => createAgent(instructions, agentName)}
              className="mt-3 px-4 py-2 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
            >
              Retry
            </button>
          </div>
        ) : (
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Bot className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-sm text-gray-600">No agent available</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CandidateWithAgent;
