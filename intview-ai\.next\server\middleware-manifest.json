{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "M6v9JqzHYXXzgvA5ZBRMX", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "EgkodQbEY9m1pIgxkdt6YkPs3VngzrM6PXdSaRub/rI=", "__NEXT_PREVIEW_MODE_ID": "b235abaed34d3c98c72c0d66caf52786", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5f6c93a7e8a0833f0b6545182d39b9d23f0b106e6ec4f3d42e502a0e0d797963", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6faf1bbf29eb3ee6c33f6da129fba2b79f6e892afcb398b7b6a7ab60d55d1e1e"}}}, "functions": {}, "sortedMiddleware": ["/"]}