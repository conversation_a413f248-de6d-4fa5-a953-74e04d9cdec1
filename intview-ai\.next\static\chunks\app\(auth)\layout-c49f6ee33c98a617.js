(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[97],{6654:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=l(2115);function r(e,t){let l=(0,n.useRef)(null),r=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=l.current;e&&(l.current=null,e());let t=r.current;t&&(r.current=null,t())}else e&&(l.current=u(e,n)),t&&(r.current=u(t,n))},[e,t])}function u(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let l=e(t);return"function"==typeof l?l:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7222:(e,t,l)=>{"use strict";l.d(t,{default:()=>u});var n=l(5155);l(2115);var r=l(6766);let u=()=>(0,n.jsxs)("div",{className:"flex flex-wrap mt-10 gap-2.5",children:[(0,n.jsx)("p",{className:"mr-10",children:"Or Continue with "}),(0,n.jsx)(r.default,{src:"/icons/facebook.svg",alt:"Facebook",width:20,height:20,className:"object-contain mr-2.5 "}),(0,n.jsx)(r.default,{src:"/icons/google.svg",alt:"Google",width:20,height:20,className:"object-contain mr-2.5"}),(0,n.jsx)(r.default,{src:"/icons/apple.svg",alt:"Apple",width:20,height:20,className:"object-contain mr-2.5 "})]})},9741:(e,t,l)=>{Promise.resolve().then(l.bind(l,7222)),Promise.resolve().then(l.t.bind(l,3063,23))}},e=>{var t=t=>e(e.s=t);e.O(0,[766,441,684,358],()=>t(9741)),_N_E=e.O()}]);