[{"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\layout.tsx": "1", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-in\\page.tsx": "2", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx": "3", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx": "4", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\job-posts\\page.tsx": "5", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx": "6", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\page.tsx": "7", "D:\\Softwares\\Ai bot\\intview-ai\\app\\api\\auth\\[...nextauth]\\route.ts": "8", "D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx": "9", "D:\\Softwares\\Ai bot\\intview-ai\\components\\CandidateImage.tsx": "10", "D:\\Softwares\\Ai bot\\intview-ai\\components\\CandidateWithAgent.tsx": "11", "D:\\Softwares\\Ai bot\\intview-ai\\components\\DIDAgent.tsx": "12", "D:\\Softwares\\Ai bot\\intview-ai\\components\\forms\\AuthForm.tsx": "13", "D:\\Softwares\\Ai bot\\intview-ai\\components\\forms\\SocialAuthForm.tsx": "14", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\Analysis.tsx": "15", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\FinishInterview.tsx": "16", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewInstructions.tsx": "17", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewRecording.tsx": "18", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewWithDID.tsx": "19", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\QuestionsPage.tsx": "20", "D:\\Softwares\\Ai bot\\intview-ai\\components\\InterviewCard.tsx": "21", "D:\\Softwares\\Ai bot\\intview-ai\\components\\InterviewLayout.tsx": "22", "D:\\Softwares\\Ai bot\\intview-ai\\components\\JobInfoCard.tsx": "23", "D:\\Softwares\\Ai bot\\intview-ai\\components\\navigation\\navbar\\index.tsx": "24", "D:\\Softwares\\Ai bot\\intview-ai\\components\\navigation\\navbar\\Theme.tsx": "25", "D:\\Softwares\\Ai bot\\intview-ai\\components\\QuestionsList.tsx": "26", "D:\\Softwares\\Ai bot\\intview-ai\\components\\sideBar.tsx": "27", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\button.tsx": "28", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\card.tsx": "29", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\dropdown-menu.tsx": "30", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\form.tsx": "31", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\input.tsx": "32", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\label.tsx": "33", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx": "34", "D:\\Softwares\\Ai bot\\intview-ai\\components\\VideoTranscript.tsx": "35", "D:\\Softwares\\Ai bot\\intview-ai\\lib\\utils.ts": "36", "D:\\Softwares\\Ai bot\\intview-ai\\lib\\validations.ts": "37"}, {"size": 1159, "mtime": 1754375401205, "results": "38", "hashOfConfig": "39"}, {"size": 419, "mtime": 1754375401206, "results": "40", "hashOfConfig": "39"}, {"size": 450, "mtime": 1754375401207, "results": "41", "hashOfConfig": "39"}, {"size": 1640, "mtime": 1754661155452, "results": "42", "hashOfConfig": "39"}, {"size": 267, "mtime": 1754654910808, "results": "43", "hashOfConfig": "39"}, {"size": 995, "mtime": 1754540332263, "results": "44", "hashOfConfig": "39"}, {"size": 1258, "mtime": 1754447219846, "results": "45", "hashOfConfig": "39"}, {"size": 120, "mtime": 1754375401212, "results": "46", "hashOfConfig": "39"}, {"size": 1694, "mtime": 1754550294826, "results": "47", "hashOfConfig": "39"}, {"size": 500, "mtime": 1754570120695, "results": "48", "hashOfConfig": "39"}, {"size": 4890, "mtime": 1754662731911, "results": "49", "hashOfConfig": "39"}, {"size": 7661, "mtime": 1754661787596, "results": "50", "hashOfConfig": "39"}, {"size": 3496, "mtime": 1754447289402, "results": "51", "hashOfConfig": "39"}, {"size": 1851, "mtime": 1754447287547, "results": "52", "hashOfConfig": "39"}, {"size": 1003, "mtime": 1754662940676, "results": "53", "hashOfConfig": "39"}, {"size": 1655, "mtime": 1754662881232, "results": "54", "hashOfConfig": "39"}, {"size": 6766, "mtime": 1754654910837, "results": "55", "hashOfConfig": "39"}, {"size": 1579, "mtime": 1754662751280, "results": "56", "hashOfConfig": "39"}, {"size": 6146, "mtime": 1754661393083, "results": "57", "hashOfConfig": "39"}, {"size": 1517, "mtime": 1754661858383, "results": "58", "hashOfConfig": "39"}, {"size": 1285, "mtime": 1754654910819, "results": "59", "hashOfConfig": "39"}, {"size": 266, "mtime": 1754488943054, "results": "60", "hashOfConfig": "39"}, {"size": 1514, "mtime": 1754544355206, "results": "61", "hashOfConfig": "39"}, {"size": 5341, "mtime": 1754654910846, "results": "62", "hashOfConfig": "39"}, {"size": 1428, "mtime": 1754383178039, "results": "63", "hashOfConfig": "39"}, {"size": 1820, "mtime": 1754661167496, "results": "64", "hashOfConfig": "39"}, {"size": 5218, "mtime": 1754654910849, "results": "65", "hashOfConfig": "39"}, {"size": 2182, "mtime": 1754375401259, "results": "66", "hashOfConfig": "39"}, {"size": 1954, "mtime": 1754654910851, "results": "67", "hashOfConfig": "39"}, {"size": 8541, "mtime": 1754375401261, "results": "68", "hashOfConfig": "39"}, {"size": 3926, "mtime": 1754375401261, "results": "69", "hashOfConfig": "39"}, {"size": 988, "mtime": 1754375401263, "results": "70", "hashOfConfig": "39"}, {"size": 635, "mtime": 1754375401263, "results": "71", "hashOfConfig": "39"}, {"size": 589, "mtime": 1754375401264, "results": "72", "hashOfConfig": "39"}, {"size": 944, "mtime": 1754539526714, "results": "73", "hashOfConfig": "39"}, {"size": 172, "mtime": 1754375401272, "results": "74", "hashOfConfig": "39"}, {"size": 1799, "mtime": 1754375401274, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "183vquo", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\layout.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-in\\page.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\job-posts\\page.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\page.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\CandidateImage.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\CandidateWithAgent.tsx", ["187", "188", "189", "190", "191"], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\DIDAgent.tsx", ["192", "193", "194"], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\forms\\AuthForm.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\forms\\SocialAuthForm.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\Analysis.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\FinishInterview.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewInstructions.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewRecording.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewWithDID.tsx", ["195", "196", "197", "198", "199", "200"], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\QuestionsPage.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\InterviewCard.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\InterviewLayout.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\JobInfoCard.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\navigation\\navbar\\index.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\navigation\\navbar\\Theme.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\QuestionsList.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\sideBar.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\button.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\card.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\dropdown-menu.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\form.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\input.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\label.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\VideoTranscript.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\lib\\utils.ts", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\lib\\validations.ts", [], [], {"ruleId": "201", "severity": 2, "message": "202", "line": 5, "column": 10, "nodeType": "203", "messageId": "204", "endLine": 5, "endColumn": 16, "suggestions": "205"}, {"ruleId": "206", "severity": 2, "message": "202", "line": 5, "column": 10, "nodeType": null, "messageId": "204", "endLine": 5, "endColumn": 16}, {"ruleId": "201", "severity": 2, "message": "207", "line": 5, "column": 18, "nodeType": "203", "messageId": "204", "endLine": 5, "endColumn": 33, "suggestions": "208"}, {"ruleId": "206", "severity": 2, "message": "207", "line": 5, "column": 18, "nodeType": null, "messageId": "204", "endLine": 5, "endColumn": 33}, {"ruleId": "206", "severity": 2, "message": "209", "line": 25, "column": 3, "nodeType": null, "messageId": "204", "endLine": 25, "endColumn": 10}, {"ruleId": "201", "severity": 2, "message": "210", "line": 4, "column": 15, "nodeType": "203", "messageId": "204", "endLine": 4, "endColumn": 22, "suggestions": "211"}, {"ruleId": "206", "severity": 2, "message": "210", "line": 4, "column": 15, "nodeType": null, "messageId": "204", "endLine": 4, "endColumn": 22}, {"ruleId": "212", "severity": 1, "message": "213", "line": 73, "column": 9, "nodeType": "214", "messageId": "215", "endLine": 73, "endColumn": 35, "fix": "216"}, {"ruleId": "201", "severity": 2, "message": "217", "line": 31, "column": 10, "nodeType": "203", "messageId": "204", "endLine": 31, "endColumn": 29, "suggestions": "218"}, {"ruleId": "206", "severity": 2, "message": "217", "line": 31, "column": 10, "nodeType": null, "messageId": "204", "endLine": 31, "endColumn": 29}, {"ruleId": "201", "severity": 2, "message": "219", "line": 41, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 41, "endColumn": 25, "suggestions": "220"}, {"ruleId": "206", "severity": 2, "message": "219", "line": 41, "column": 9, "nodeType": null, "messageId": "204", "endLine": 41, "endColumn": 25}, {"ruleId": "201", "severity": 2, "message": "221", "line": 49, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 49, "endColumn": 23, "suggestions": "222"}, {"ruleId": "206", "severity": 2, "message": "221", "line": 49, "column": 9, "nodeType": null, "messageId": "204", "endLine": 49, "endColumn": 23}, "no-unused-vars", "'motion' is defined but never used.", "Identifier", "unusedVar", ["223"], "@typescript-eslint/no-unused-vars", "'AnimatePresence' is defined but never used.", ["224"], "'message' is defined but never used.", "'Loader2' is defined but never used.", ["225"], "object-shorthand", "Expected property shorthand.", "Property", "expectedProperty<PERSON><PERSON><PERSON>d", {"range": "226", "text": "227"}, "'currentQuestionText' is assigned a value but never used.", ["228"], "'handleVideoReady' is assigned a value but never used.", ["229"], "'handleVideoEnd' is assigned a value but never used.", ["230"], {"messageId": "231", "data": "232", "fix": "233", "desc": "234"}, {"messageId": "231", "data": "235", "fix": "236", "desc": "237"}, {"messageId": "231", "data": "238", "fix": "239", "desc": "240"}, [2071, 2097], "instructions", {"messageId": "231", "data": "241", "fix": "242", "desc": "243"}, {"messageId": "231", "data": "244", "fix": "245", "desc": "246"}, {"messageId": "231", "data": "247", "fix": "248", "desc": "249"}, "removeVar", {"varName": "250"}, {"range": "251", "text": "252"}, "Remove unused variable 'motion'.", {"varName": "253"}, {"range": "254", "text": "252"}, "Remove unused variable 'AnimatePresence'.", {"varName": "255"}, {"range": "256", "text": "252"}, "Remove unused variable 'Loader2'.", {"varName": "257"}, {"range": "258", "text": "252"}, "Remove unused variable 'currentQuestionText'.", {"varName": "259"}, {"range": "260", "text": "252"}, "Remove unused variable 'handleVideoReady'.", {"varName": "261"}, {"range": "262", "text": "252"}, "Remove unused variable 'handleVideoEnd'.", "motion", [146, 153], "", "AnimatePresence", [152, 169], "Loader2", [126, 135], "currentQuestionText", [944, 963], "handleVideoReady", [1267, 1480], "handleVideoEnd", [1486, 1669]]